; ModuleID = 'main'
source_filename = "main"

@format_str = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1
@format_str.1 = private unnamed_addr constant [4 x i8] c"%d\0A\00", align 1

declare i32 @printf(ptr, ...)

define i32 @main() {
entry:
  %call_factorial = call i32 @factorial(i32 5)
  %printf_call = call i32 (ptr, ...) @printf(ptr @format_str, i32 %call_factorial)
  %call_add = call i32 @add(i32 5, i32 10)
  %printf_call1 = call i32 (ptr, ...) @printf(ptr @format_str.1, i32 %call_add)
  ret i32 0
}

define i32 @factorial(i32 %0) {
entry:
  %n = alloca i32, align 4
  store i32 %0, ptr %n, align 4
  %n1 = load i32, ptr %n, align 4
  %eq = icmp eq i32 %n1, 0
  %eq_ext = zext i1 %eq to i32
  %n2 = load i32, ptr %n, align 4
  %eq3 = icmp eq i32 %n2, 1
  %eq_ext4 = zext i1 %eq3 to i32
  %or = or i32 %eq_ext, %eq_ext4
  %condition = icmp ne i32 %or, 0
  br i1 %condition, label %--if-then, label %--if-else

--if-then:                                        ; preds = %entry
  ret i32 1
  br label %--if-merge

--if-else:                                        ; preds = %entry
  %n5 = load i32, ptr %n, align 4
  %n6 = load i32, ptr %n, align 4
  %sub = sub i32 %n6, 1
  %call_factorial = call i32 @factorial(i32 %sub)
  %mul = mul i32 %n5, %call_factorial
  ret i32 %mul
  br label %--if-merge

--if-merge:                                       ; preds = %--if-else, %--if-then
  ret i32 0
}

define i32 @add(i32 %0, i32 %1) {
entry:
  %a = alloca i32, align 4
  store i32 %0, ptr %a, align 4
  %b = alloca i32, align 4
  store i32 %1, ptr %b, align 4
  %a1 = load i32, ptr %a, align 4
  %b2 = load i32, ptr %b, align 4
  %add = add i32 %a1, %b2
  ret i32 %add
  ret i32 0
}
